import { StockStatus } from '../types'

// Utility function to format Chilean pesos
export const formatCLP = (amount: number): string => {
  return new Intl.NumberFormat('es-CL', {
    style: 'currency',
    currency: 'CLP',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

// Utility function to get stock status
export const getStockStatus = (stock: number): StockStatus => {
  if (stock === 0) return 'out-of-stock';
  if (stock <= 5) return 'critical';
  if (stock <= 10) return 'low';
  return 'normal';
}

// Utility function to calculate IVA
export const calculateIVA = (amount: number, rate: number = 0.19): number => {
  return amount * rate
}

// Utility function to calculate total with IVA
export const calculateTotalWithIVA = (amount: number, rate: number = 0.19): number => {
  return amount * (1 + rate)
}
