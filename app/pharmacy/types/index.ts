export interface Product {
  id: string
  name: string
  price: number
  category: string
  stock: number
  prescription: boolean
  laboratory: string
  image: string
  sku: string
  barcode?: string
}

export interface CartItem extends Product {
  quantity: number
}

export type StockStatus = 'out-of-stock' | 'critical' | 'low' | 'normal'

export type PaymentMethod = 'Efectivo' | 'Cheque' | 'Transferencia' | 'Tarjeta'

export interface Category {
  name: string
  icon: any // Lucide icon component
}
